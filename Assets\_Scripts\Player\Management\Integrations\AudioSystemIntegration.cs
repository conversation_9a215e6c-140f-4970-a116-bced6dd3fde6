using UnityEngine;
using BTR.Player;

namespace BTR.Player
{
    /// <summary>
    /// Integration between player system and the audio system.
    /// Handles audio event coordination and music state management.
    /// </summary>
    public class AudioSystemIntegration : PlayerSystemIntegrationBase
    {
        private AudioManager audioManager;
        private PlayerEntity currentPlayer;
        private PlayerAudioComponent playerAudioComponent;

        protected override void OnInitialize()
        {
            // Get AudioManager instance
            audioManager = AudioManager.Instance;

            if (audioManager == null)
            {
                Debug.LogWarning("[AudioSystemIntegration] AudioManager not found");
                return;
            }

            Debug.Log("[AudioSystemIntegration] Initialized");
        }

        protected override void OnCleanup()
        {
            UnsubscribeFromPlayerAudioEvents();
            currentPlayer = null;
            playerAudioComponent = null;

            Debug.Log("[AudioSystemIntegration] Cleaned up");
        }

        protected override void HandlePlayerSpawned(PlayerEntity player)
        {
            currentPlayer = player;
            playerAudioComponent = player.GetComponent<PlayerAudioComponent>();

            SubscribeToPlayerAudioEvents();

            // Set music state for player spawn
            if (audioManager != null)
            {
                audioManager.SetMusicParameter("Player_State", 1f); // Player active
                audioManager.SetMusicParameter("Combat_State", 0f); // Not in combat
                audioManager.SetMusicParameter("Time_State", 0f); // Normal time
            }

            Debug.Log("[AudioSystemIntegration] Player spawned - audio events subscribed");
        }

        protected override void HandlePlayerDestroyed(PlayerEntity player)
        {
            UnsubscribeFromPlayerAudioEvents();

            // Set music state for player destruction
            if (audioManager != null)
            {
                audioManager.SetMusicParameter("Player_State", 0f); // Player inactive
            }

            currentPlayer = null;
            playerAudioComponent = null;

            Debug.Log("[AudioSystemIntegration] Player destroyed - audio events unsubscribed");
        }

        protected override void HandlePlayerStateChanged(PlayerState oldState, PlayerState newState)
        {
            if (audioManager == null)
                return;

            // Update music parameters based on player state
            switch (newState)
            {
                case PlayerState.Dead:
                    audioManager.SetMusicParameter("Player_State", 0f);
                    audioManager.SetMusicParameter("Combat_State", 0f);
                    audioManager.SetMusicParameter("Time_State", 0f);
                    break;

                case PlayerState.Active:
                    audioManager.SetMusicParameter("Player_State", 1f);
                    if (oldState == PlayerState.Dead)
                    {
                        // Player respawned - AudioManager doesn't have PlayOneShot
                        Debug.Log("[AudioSystemIntegration] Player respawned");
                    }
                    break;

                case PlayerState.Combat:
                    audioManager.SetMusicParameter("Combat_State", 1f);
                    break;

                case PlayerState.TimeControl:
                    HandleTimeControlAudio(oldState);
                    break;

                case PlayerState.Dodging:
                    if (playerAudioComponent != null)
                    {
                        playerAudioComponent.PlayDodge();
                    }
                    break;
            }

            // Reset combat state when leaving combat
            if (oldState == PlayerState.Combat && newState != PlayerState.Combat)
            {
                audioManager.SetMusicParameter("Combat_State", 0f);
            }

            // Reset time state when leaving time control
            if (oldState == PlayerState.TimeControl && newState != PlayerState.TimeControl)
            {
                audioManager.SetMusicParameter("Time_State", 0f);
                if (playerAudioComponent != null)
                {
                    playerAudioComponent.PlayTimeRestore();
                }
            }
        }

        private void SubscribeToPlayerAudioEvents()
        {
            if (currentPlayer == null)
                return;

            // Subscribe to health component events
            var healthComponent = currentPlayer.GetComponent<PlayerHealthComponent>();
            if (healthComponent != null)
            {
                healthComponent.OnDamageReceived += OnPlayerDamaged;
                healthComponent.OnDeath += OnPlayerDied;
                healthComponent.OnHealthChanged += OnPlayerHealthChanged;
            }

            // Subscribe to ground detection events
            var groundDetection = currentPlayer.GetComponent<PlayerGroundDetectionComponent>();
            if (groundDetection != null)
            {
                groundDetection.OnGroundedChanged += OnPlayerGroundedChanged;
            }

            // Subscribe to time control events
            var timeControlComponent = currentPlayer.GetComponent<PlayerTimeControlComponent>();
            if (timeControlComponent != null)
            {
                // Note: Would need to add events to time control component
                // timeControlComponent.OnRewindStarted += OnRewindStarted;
                // timeControlComponent.OnSlowTimeStarted += OnSlowTimeStarted;
            }
        }

        private void UnsubscribeFromPlayerAudioEvents()
        {
            if (currentPlayer == null)
                return;

            // Unsubscribe from health component events
            var healthComponent = currentPlayer.GetComponent<PlayerHealthComponent>();
            if (healthComponent != null)
            {
                healthComponent.OnDamageReceived -= OnPlayerDamaged;
                healthComponent.OnDeath -= OnPlayerDied;
                healthComponent.OnHealthChanged -= OnPlayerHealthChanged;
            }

            // Unsubscribe from ground detection events
            var groundDetection = currentPlayer.GetComponent<PlayerGroundDetectionComponent>();
            if (groundDetection != null)
            {
                groundDetection.OnGroundedChanged -= OnPlayerGroundedChanged;
            }
        }

        private void HandleTimeControlAudio(PlayerState oldState)
        {
            if (audioManager == null || currentPlayer == null)
                return;

            var timeControlComponent = currentPlayer.GetComponent<PlayerTimeControlComponent>();
            if (timeControlComponent == null)
                return;

            if (timeControlComponent.IsRewinding())
            {
                audioManager.SetMusicParameter("Time_State", 1f); // Rewind
                if (playerAudioComponent != null)
                {
                    playerAudioComponent.PlayRewind();
                }
            }
            else if (timeControlComponent.IsSlowingTime())
            {
                audioManager.SetMusicParameter("Time_State", 2f); // Slow time
                if (playerAudioComponent != null)
                {
                    playerAudioComponent.PlaySlowTime();
                }
            }
        }

        #region Event Handlers

        private void OnPlayerDamaged(float damage)
        {
            if (playerAudioComponent != null)
            {
                playerAudioComponent.PlayDamage();
            }

            // Play global damage audio
            if (audioManager != null)
            {
                // AudioManager doesn't have PlayOneShot, use player audio component instead
                Debug.Log("[AudioSystemIntegration] Player damaged");
            }
        }

        private void OnPlayerDied()
        {
            if (playerAudioComponent != null)
            {
                playerAudioComponent.PlayDeath();
            }

            // Set music to death state
            if (audioManager != null)
            {
                audioManager.SetMusicParameter("Player_State", 0f);
                // AudioManager doesn't have PlayOneShot, use player audio component instead
                Debug.Log("[AudioSystemIntegration] Player died");
            }
        }

        private void OnPlayerHealthChanged(float newHealth)
        {
            // Could play heal sound if health increased
            // This would need additional logic to determine if it's healing vs damage
            var healthComponent = currentPlayer?.GetComponent<PlayerHealthComponent>();
            if (healthComponent != null)
            {
                float healthPercentage = healthComponent.GetHealthPercentage();

                // Update music intensity based on health
                if (audioManager != null)
                {
                    audioManager.SetMusicParameter("Health", healthPercentage);
                }

                // Play heal sound if health increased significantly
                if (newHealth > healthComponent.GetCurrentHealth() + 10f)
                {
                    if (playerAudioComponent != null)
                    {
                        playerAudioComponent.PlayHeal();
                    }
                }
            }
        }

        #endregion

        #region Public API

        /// <summary>
        /// Set a music parameter for player-related audio
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <param name="value">Parameter value</param>
        public void SetMusicParameter(string parameterName, float value)
        {
            if (audioManager != null)
            {
                audioManager.SetMusicParameter(parameterName, value);
            }
        }

        /// <summary>
        /// Play a one-shot audio event
        /// </summary>
        /// <param name="eventPath">FMOD event path</param>
        public void PlayOneShot(string eventPath)
        {
            if (audioManager != null)
            {
                // AudioManager doesn't have PlayOneShot, use GetOrCreateInstance instead
                var instance = audioManager.GetOrCreateInstance(eventPath);
                instance.start();
                instance.release();
            }
        }

        /// <summary>
        /// Enable or disable player audio
        /// </summary>
        /// <param name="enabled">Whether audio should be enabled</param>
        public void SetPlayerAudioEnabled(bool enabled)
        {
            if (playerAudioComponent != null)
            {
                playerAudioComponent.SetAudioEnabled(enabled);
            }
        }

        /// <summary>
        /// Set player audio volume
        /// </summary>
        /// <param name="volume">Volume (0-1)</param>
        public void SetPlayerAudioVolume(float volume)
        {
            if (playerAudioComponent != null)
            {
                playerAudioComponent.SetMasterVolume(volume);
            }
        }

        #endregion
    }
}
ipts as